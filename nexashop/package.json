{"name": "nexashop", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.10", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.16.0", "lucide-react": "^0.513.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "stripe": "^18.2.1", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.17", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "typescript": "^5"}}