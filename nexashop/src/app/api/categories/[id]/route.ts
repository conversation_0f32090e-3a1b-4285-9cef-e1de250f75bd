import { NextRequest, NextResponse } from 'next/server'
import { getCategoryById, getProducts } from '@/lib/data'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const category = getCategoryById(params.id)
    
    if (!category) {
      return NextResponse.json(
        { success: false, error: 'Category not found' },
        { status: 404 }
      )
    }

    const products = getProducts(params.id)

    return NextResponse.json({
      success: true,
      data: {
        ...category,
        products
      }
    })
  } catch (error) {
    console.error('Error fetching category:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch category' },
      { status: 500 }
    )
  }
}
