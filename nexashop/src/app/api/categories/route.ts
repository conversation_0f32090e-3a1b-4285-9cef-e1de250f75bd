import { NextRequest, NextResponse } from 'next/server'
import { getCategories } from '@/lib/data'

export async function GET(request: NextRequest) {
  try {
    const categories = getCategories()

    return NextResponse.json({
      success: true,
      data: categories,
      count: categories.length
    })
  } catch (error) {
    console.error('Error fetching categories:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch categories' },
      { status: 500 }
    )
  }
}
