import { NextRequest, NextResponse } from 'next/server'
import { getProductById, getReviewsByProductId } from '@/lib/data'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const product = getProductById(params.id)
    
    if (!product) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      )
    }

    const reviews = getReviewsByProductId(params.id)

    return NextResponse.json({
      success: true,
      data: {
        ...product,
        reviews
      }
    })
  } catch (error) {
    console.error('Error fetching product:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch product' },
      { status: 500 }
    )
  }
}
