import { NextRequest, NextResponse } from 'next/server'
import { getProducts, searchProducts } from '@/lib/data'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get('category')
    const limit = searchParams.get('limit')
    const search = searchParams.get('search')

    let products

    if (search) {
      products = searchProducts(search)
    } else {
      products = getProducts(
        categoryId || undefined,
        limit ? parseInt(limit) : undefined
      )
    }

    return NextResponse.json({
      success: true,
      data: products,
      count: products.length
    })
  } catch (error) {
    console.error('Error fetching products:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch products' },
      { status: 500 }
    )
  }
}
