import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    console.log('Setting up database...')
    
    // Categories data
    const categories = [
      { name: 'Electronics', description: 'Latest gadgets and electronic devices' },
      { name: 'Fashion', description: 'Trendy clothing and accessories' },
      { name: 'Home & Garden', description: 'Everything for your home and garden' },
      { name: 'Sports', description: 'Sports equipment and fitness gear' },
      { name: 'Books', description: 'Books, magazines, and educational materials' }
    ]

    // Products data
    const products = [
      // Electronics
      { name: 'iPhone 15 Pro', description: 'Latest iPhone with advanced camera system and A17 Pro chip', price: 999.99, image_url: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=500', category_name: 'Electronics', stock_quantity: 50 },
      { name: 'MacBook Air M3', description: 'Powerful laptop with M3 chip and all-day battery life', price: 1299.99, image_url: 'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=500', category_name: 'Electronics', stock_quantity: 30 },
      { name: 'Sony WH-1000XM5', description: 'Premium noise-canceling wireless headphones', price: 399.99, image_url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500', category_name: 'Electronics', stock_quantity: 75 },
      { name: 'Samsung 4K Smart TV', description: '55-inch 4K UHD Smart TV with HDR and streaming apps', price: 799.99, image_url: 'https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=500', category_name: 'Electronics', stock_quantity: 25 },
      
      // Fashion
      { name: 'Designer Leather Jacket', description: 'Premium genuine leather jacket with modern fit', price: 299.99, image_url: 'https://images.unsplash.com/photo-1551028719-00167b16eac5?w=500', category_name: 'Fashion', stock_quantity: 40 },
      { name: 'Classic Denim Jeans', description: 'Comfortable straight-fit denim jeans in classic blue', price: 79.99, image_url: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=500', category_name: 'Fashion', stock_quantity: 100 },
      { name: 'Luxury Watch', description: 'Elegant stainless steel watch with automatic movement', price: 599.99, image_url: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=500', category_name: 'Fashion', stock_quantity: 20 },
      { name: 'Designer Sneakers', description: 'Comfortable and stylish sneakers for everyday wear', price: 149.99, image_url: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=500', category_name: 'Fashion', stock_quantity: 60 },
      
      // Home & Garden
      { name: 'Modern Coffee Table', description: 'Sleek glass-top coffee table with wooden legs', price: 249.99, image_url: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=500', category_name: 'Home & Garden', stock_quantity: 15 },
      { name: 'Indoor Plant Set', description: 'Collection of 3 low-maintenance indoor plants with pots', price: 89.99, image_url: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=500', category_name: 'Home & Garden', stock_quantity: 35 },
      
      // Sports
      { name: 'Professional Tennis Racket', description: 'High-quality tennis racket for intermediate to advanced players', price: 199.99, image_url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=500', category_name: 'Sports', stock_quantity: 25 },
      { name: 'Yoga Mat Premium', description: 'Non-slip premium yoga mat with carrying strap', price: 49.99, image_url: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=500', category_name: 'Sports', stock_quantity: 80 },
      
      // Books
      { name: 'JavaScript: The Complete Guide', description: 'Comprehensive guide to modern JavaScript development', price: 39.99, image_url: 'https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=500', category_name: 'Books', stock_quantity: 50 },
      { name: 'Design Thinking Handbook', description: 'Learn the principles and practices of design thinking', price: 29.99, image_url: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=500', category_name: 'Books', stock_quantity: 40 }
    ]
    
    // Insert categories
    console.log('Inserting categories...')
    const { data: categoriesData, error: categoriesError } = await supabase
      .from('categories')
      .upsert(categories, { onConflict: 'name' })
      .select()
    
    if (categoriesError) {
      console.error('Error inserting categories:', categoriesError)
      return NextResponse.json({ 
        success: false, 
        error: 'Failed to insert categories',
        details: categoriesError 
      }, { status: 500 })
    }
    
    console.log('Categories inserted:', categoriesData?.length)
    
    // Insert products
    console.log('Inserting products...')
    let successCount = 0
    
    for (const product of products) {
      const category = categoriesData?.find(c => c.name === product.category_name)
      if (category) {
        const { error: productError } = await supabase
          .from('products')
          .upsert({
            name: product.name,
            description: product.description,
            price: product.price,
            image_url: product.image_url,
            category_id: category.id,
            stock_quantity: product.stock_quantity
          }, { onConflict: 'name' })
        
        if (productError) {
          console.error('Error inserting product:', product.name, productError)
        } else {
          successCount++
        }
      }
    }
    
    return NextResponse.json({
      success: true,
      message: 'Database setup completed successfully!',
      data: {
        categories: categoriesData?.length || 0,
        products: successCount
      }
    })
    
  } catch (error) {
    console.error('Error setting up database:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to setup database' },
      { status: 500 }
    )
  }
}
